{"models": {"main": {"provider": "openai", "modelId": "gemini-2.5-pro-chataia<PERSON>", "baseURL": "https://www.chataiapi.com/v1", "maxTokens": 120000, "temperature": 0.2}, "research": {"provider": "openai", "modelId": "gemini-2.5-pro-chataia<PERSON>", "baseURL": "https://www.chataiapi.com/v1", "maxTokens": 8700, "temperature": 0.1}, "fallback": {"provider": "openai", "modelId": "gemini-2.5-pro-chataia<PERSON>", "baseURL": "https://www.chataiapi.com/v1", "maxTokens": 8192, "temperature": 0.1}}, "global": {"logLevel": "info", "debug": false, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseURL": "http://localhost:11434/api", "azureOpenaiBaseURL": "https://your-endpoint.openai.azure.com/", "bedrockBaseURL": "https://bedrock.us-east-1.amazonaws.com"}}