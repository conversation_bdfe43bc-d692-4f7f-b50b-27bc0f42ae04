---
description: Glossary of other Roo Code rules
globs: **/*
alwaysApply: true
---
# Glossary of Task Master Roo Code Rules

This file provides a quick reference to the purpose of each rule file located in the `.roo/rules` directory.

- **[`architecture.md`](mdc:.roo/rules/architecture.md)**: Describes the high-level architecture of the Task Master CLI application.
- **[`changeset.md`](mdc:.roo/rules/changeset.md)**: Guidelines for using Changesets (npm run changeset) to manage versioning and changelogs.
- **[`commands.md`](mdc:.roo/rules/commands.md)**: Guidelines for implementing CLI commands using Commander.js.
- **[`cursor_rules.md`](mdc:.roo/rules/cursor_rules.md)**: Guidelines for creating and maintaining Roo Code rules to ensure consistency and effectiveness.
- **[`dependencies.md`](mdc:.roo/rules/dependencies.md)**: Guidelines for managing task dependencies and relationships.
- **[`dev_workflow.md`](mdc:.roo/rules/dev_workflow.md)**: Guide for using Task Master to manage task-driven development workflows.
- **[`glossary.md`](mdc:.roo/rules/glossary.md)**: This file; provides a glossary of other Roo Code rules.
- **[`mcp.md`](mdc:.roo/rules/mcp.md)**: Guidelines for implementing and interacting with the Task Master MCP Server.
- **[`new_features.md`](mdc:.roo/rules/new_features.md)**: Guidelines for integrating new features into the Task Master CLI.
- **[`self_improve.md`](mdc:.roo/rules/self_improve.md)**: Guidelines for continuously improving Roo Code rules based on emerging code patterns and best practices.
- **[`taskmaster.md`](mdc:.roo/rules/taskmaster.md)**: Comprehensive reference for Taskmaster MCP tools and CLI commands.
- **[`tasks.md`](mdc:.roo/rules/tasks.md)**: Guidelines for implementing task management operations.
- **[`tests.md`](mdc:.roo/rules/tests.md)**: Guidelines for implementing and maintaining tests for Task Master CLI.
- **[`ui.md`](mdc:.roo/rules/ui.md)**: Guidelines for implementing and maintaining user interface components.
- **[`utilities.md`](mdc:.roo/rules/utilities.md)**: Guidelines for implementing utility functions.
- **[`telemetry.md`](mdc:.roo/rules/telemetry.md)**: Guidelines for integrating AI usage telemetry across Task Master.

